'use client'

import { motion } from 'framer-motion'
import { CheckCircle, Clock, ArrowRight, Zap, Users, TrendingUp, Target, Shield, Lightbulb } from 'lucide-react'
import Link from 'next/link'

export default function SolutionsClient() {
  const businessChallenges = [
    {
      icon: <Clock className="w-8 h-8" />,
      challenge: 'Working Until 9pm Every Night',
      solution: 'Automated Systems That Work While You Sleep',
      description: 'Stop being trapped in your business. Our automation handles bookings, follow-ups, and customer communication 24/7.',
      transformation: 'From 60-hour weeks to 40-hour weeks with higher revenue'
    },
    {
      icon: <Target className="w-8 h-8" />,
      challenge: 'Losing Prospects to Competitors',
      solution: 'Instant Response & Smart Follow-Up',
      description: 'Never lose another prospect to slow response times. Automated systems capture and nurture every lead.',
      transformation: 'From 20% conversion to 89% conversion rate'
    },
    {
      icon: <Users className="w-8 h-8" />,
      challenge: 'Customers Forgetting Appointments',
      solution: 'Intelligent Reminder Sequences',
      description: 'Reduce no-shows by 85% with perfectly timed SMS and email reminders that customers actually read.',
      transformation: 'From 30% no-shows to less than 5% no-shows'
    },
    {
      icon: <TrendingUp className="w-8 h-8" />,
      challenge: 'Inconsistent Monthly Revenue',
      solution: 'Predictable Customer Flow',
      description: 'Build a steady stream of repeat customers through automated follow-up and re-booking sequences.',
      transformation: 'From feast-or-famine to predictable monthly growth'
    }
  ]

  const solutionCategories = [
    {
      icon: <Zap className="w-12 h-12" />,
      title: 'Customer Acquisition',
      subtitle: 'Never Miss Another Lead',
      features: [
        '24/7 automated lead capture',
        'Instant response to inquiries',
        'Smart lead qualification',
        'Competitor-proof follow-up'
      ],
      result: 'Convert 89% of prospects into customers'
    },
    {
      icon: <Shield className="w-12 h-12" />,
      title: 'Customer Retention',
      subtitle: 'Turn One-Time Buyers Into Loyal Customers',
      features: [
        'Automated thank you sequences',
        'Review request automation',
        'Re-booking reminders',
        'Loyalty program management'
      ],
      result: 'Increase repeat business by 40%'
    },
    {
      icon: <Lightbulb className="w-12 h-12" />,
      title: 'Business Intelligence',
      subtitle: 'Make Data-Driven Decisions',
      features: [
        'Real-time conversion tracking',
        'Customer journey analytics',
        'Revenue forecasting',
        'Performance optimization'
      ],
      result: 'Identify £10,000+ in hidden revenue opportunities'
    }
  ]

  return (
    <div className="relative w-full overflow-hidden">
      {/* Hero Section - Psychological Engineering Framework */}
      <section className="header-spacing py-20 relative">
        <div className="container relative z-10 mx-auto px-4 md:px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
            className="mx-auto max-w-4xl text-center"
          >
            <div className="bg-background border border-primary-200/20 dark:border-primary-800/20 rounded-3xl p-12 shadow-lg backdrop-blur-sm">
              {/* Curiosity Hook */}
              <div className="bg-primary-50 dark:bg-primary-950/30 border border-primary-200/30 dark:border-primary-800/30 rounded-2xl p-4 mb-8">
                <p className="text-primary-700 dark:text-primary-400 font-medium">
                  Something's changing across UK businesses...
                </p>
              </div>

              <h1 className="text-4xl font-bold text-foreground sm:text-5xl md:text-6xl mb-6">
                The <span className="text-primary-700 dark:text-primary-400">Quiet Revolution</span> Transforming Local Businesses
              </h1>

              <p className="text-xl text-muted-foreground leading-relaxed mb-6">
                <strong className="text-foreground">While you were working late, something shifted.</strong> Forward-thinking UK business owners discovered solutions that convert 89% of prospects into customers. The question isn't IF you'll join them, but WHEN.
              </p>
              
              {/* Social Proof */}
              <div className="grid md:grid-cols-3 gap-6 mb-8">
                <div className="bg-green-50 dark:bg-green-950/30 border border-green-200/30 dark:border-green-800/30 rounded-2xl p-4">
                  <div className="text-2xl font-bold text-green-700 dark:text-green-400 mb-1">
                    500+
                  </div>
                  <p className="text-sm text-muted-foreground">UK businesses transformed</p>
                </div>

                <div className="bg-green-50 dark:bg-green-950/30 border border-green-200/30 dark:border-green-800/30 rounded-2xl p-4">
                  <div className="text-2xl font-bold text-green-700 dark:text-green-400 mb-1">
                    89%
                  </div>
                  <p className="text-sm text-muted-foreground">Average conversion rate</p>
                </div>

                <div className="bg-green-50 dark:bg-green-950/30 border border-green-200/30 dark:border-green-800/30 rounded-2xl p-4">
                  <div className="text-2xl font-bold text-green-700 dark:text-green-400 mb-1">
                    20+
                  </div>
                  <p className="text-sm text-muted-foreground">Hours saved weekly</p>
                </div>
              </div>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link 
                  href="/demo" 
                  className="inline-flex items-center justify-center px-8 py-4 bg-primary-700 hover:bg-primary-800 dark:bg-primary-600 dark:hover:bg-primary-700 text-white rounded-2xl transition-all duration-300 font-medium shadow-lg hover:shadow-xl text-lg"
                >
                  Discover Your Business Evolution
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
                
                <Link 
                  href="/contact" 
                  className="inline-flex items-center justify-center px-8 py-4 border-2 border-primary-200 dark:border-primary-800 text-primary-700 dark:text-primary-400 rounded-2xl hover:border-primary-300 dark:hover:border-primary-700 hover:bg-primary-50 dark:hover:bg-primary-900/20 transition-all duration-300 font-medium text-lg"
                >
                  Join the Movement
                </Link>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Business Challenges & Solutions */}
      <section className="py-20">
        <div className="container mx-auto px-4 md:px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-foreground sm:text-4xl mb-6">
              What <span className="text-primary-700 dark:text-primary-400">Successful Businesses</span> Do Differently
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              <strong className="text-foreground">The pattern all thriving businesses follow.</strong> They've moved beyond manual processes to intelligent automation that works 24/7.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 gap-8 max-w-6xl mx-auto">
            {businessChallenges.map((item, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-background border border-primary-200/20 dark:border-primary-800/20 rounded-3xl p-8 shadow-lg"
              >
                <div className="flex items-start gap-4">
                  <div className="flex-shrink-0 w-12 h-12 bg-primary-100 dark:bg-primary-900/30 rounded-2xl flex items-center justify-center text-primary-700 dark:text-primary-400">
                    {item.icon}
                  </div>
                  <div className="flex-1">
                    <div className="bg-red-50 dark:bg-red-950/30 border border-red-200/30 dark:border-red-800/30 rounded-xl p-3 mb-4">
                      <h3 className="text-lg font-semibold text-red-700 dark:text-red-400 mb-1">
                        The Problem:
                      </h3>
                      <p className="text-sm text-red-600 dark:text-red-300">
                        {item.challenge}
                      </p>
                    </div>
                    
                    <div className="bg-green-50 dark:bg-green-950/30 border border-green-200/30 dark:border-green-800/30 rounded-xl p-3 mb-4">
                      <h3 className="text-lg font-semibold text-green-700 dark:text-green-400 mb-1">
                        The Solution:
                      </h3>
                      <p className="text-sm text-green-600 dark:text-green-300">
                        {item.solution}
                      </p>
                    </div>
                    
                    <p className="text-muted-foreground mb-4 leading-relaxed">
                      {item.description}
                    </p>
                    
                    <div className="bg-primary-50 dark:bg-primary-950/30 border border-primary-200/30 dark:border-primary-800/30 rounded-xl p-3">
                      <p className="text-sm text-foreground font-medium">
                        <CheckCircle className="w-4 h-4 text-primary-700 dark:text-primary-400 inline mr-2" />
                        {item.transformation}
                      </p>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Solution Categories */}
      <section className="py-20">
        <div className="container mx-auto px-4 md:px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-foreground sm:text-4xl mb-6">
              Complete <span className="text-primary-700 dark:text-primary-400">Business Solutions</span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              <strong className="text-foreground">This isn't about technology - it's about evolution.</strong> Join the businesses that have already made the shift to intelligent automation.
            </p>
          </motion.div>

          <div className="grid lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
            {solutionCategories.map((category, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
                className="bg-background border border-primary-200/20 dark:border-primary-800/20 rounded-3xl p-8 shadow-lg text-center"
              >
                <div className="w-20 h-20 bg-primary-100 dark:bg-primary-900/30 rounded-3xl flex items-center justify-center text-primary-700 dark:text-primary-400 mx-auto mb-6">
                  {category.icon}
                </div>

                <h3 className="text-2xl font-bold text-foreground mb-2">
                  {category.title}
                </h3>

                <p className="text-lg text-primary-700 dark:text-primary-400 font-medium mb-6">
                  {category.subtitle}
                </p>

                <div className="space-y-3 mb-6">
                  {category.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-center text-left">
                      <CheckCircle className="w-5 h-5 text-primary-700 dark:text-primary-400 mr-3 flex-shrink-0" />
                      <span className="text-muted-foreground">{feature}</span>
                    </div>
                  ))}
                </div>

                <div className="bg-green-50 dark:bg-green-950/30 border border-green-200/30 dark:border-green-800/30 rounded-2xl p-4">
                  <p className="text-green-700 dark:text-green-400 font-semibold">
                    {category.result}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* The Inevitability Section - Psychological Engineering */}
      <section className="py-20">
        <div className="container mx-auto px-4 md:px-6">
          <div className="bg-primary-50 dark:bg-primary-950/30 border border-primary-200/30 dark:border-primary-800/30 rounded-3xl p-12 shadow-lg max-w-5xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, ease: 'easeOut' }}
              viewport={{ once: true }}
              className="text-center"
            >
              <h2 className="text-3xl font-bold text-foreground mb-6 sm:text-4xl">
                This Shift Is Happening <span className="text-primary-700 dark:text-primary-400">Whether You Participate Or Not</span>
              </h2>

              <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
                <strong className="text-foreground">The businesses that survive will adapt.</strong> Customer expectations have changed. They expect instant responses, seamless experiences, and consistent communication. Manual processes can't compete.
              </p>

              <div className="grid md:grid-cols-2 gap-8 mb-8">
                <div className="bg-background border border-red-200/30 dark:border-red-800/30 rounded-2xl p-6">
                  <h3 className="text-xl font-semibold text-red-700 dark:text-red-400 mb-4">
                    Businesses That Don't Adapt
                  </h3>
                  <ul className="space-y-2 text-left">
                    <li className="flex items-center text-muted-foreground">
                      <span className="w-2 h-2 bg-red-500 rounded-full mr-3"></span>
                      Lose prospects to faster competitors
                    </li>
                    <li className="flex items-center text-muted-foreground">
                      <span className="w-2 h-2 bg-red-500 rounded-full mr-3"></span>
                      Work longer hours for less revenue
                    </li>
                    <li className="flex items-center text-muted-foreground">
                      <span className="w-2 h-2 bg-red-500 rounded-full mr-3"></span>
                      Struggle with inconsistent cash flow
                    </li>
                    <li className="flex items-center text-muted-foreground">
                      <span className="w-2 h-2 bg-red-500 rounded-full mr-3"></span>
                      Get left behind by the market
                    </li>
                  </ul>
                </div>

                <div className="bg-background border border-green-200/30 dark:border-green-800/30 rounded-2xl p-6">
                  <h3 className="text-xl font-semibold text-green-700 dark:text-green-400 mb-4">
                    Businesses That Embrace Change
                  </h3>
                  <ul className="space-y-2 text-left">
                    <li className="flex items-center text-muted-foreground">
                      <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                      Convert 89% of prospects into customers
                    </li>
                    <li className="flex items-center text-muted-foreground">
                      <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                      Work smarter, not harder
                    </li>
                    <li className="flex items-center text-muted-foreground">
                      <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                      Enjoy predictable revenue growth
                    </li>
                    <li className="flex items-center text-muted-foreground">
                      <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                      Lead their market
                    </li>
                  </ul>
                </div>
              </div>

              <p className="text-lg text-muted-foreground mb-8">
                <strong className="text-foreground">You can lead this change or be forced to follow.</strong> The choice is yours, but the window is closing.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  href="/demo"
                  className="inline-flex items-center justify-center px-8 py-4 bg-primary-700 hover:bg-primary-800 dark:bg-primary-600 dark:hover:bg-primary-700 text-white rounded-2xl transition-all duration-300 font-medium shadow-lg hover:shadow-xl text-lg"
                >
                  Join the Successful Group
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>

                <Link
                  href="/contact"
                  className="inline-flex items-center justify-center px-8 py-4 border-2 border-primary-200 dark:border-primary-800 text-primary-700 dark:text-primary-400 rounded-2xl hover:border-primary-300 dark:hover:border-primary-700 hover:bg-primary-50 dark:hover:bg-primary-900/20 transition-all duration-300 font-medium text-lg"
                >
                  Explore Your Options
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Local Business Focus */}
      <section className="py-20">
        <div className="container mx-auto px-4 md:px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl font-bold text-foreground sm:text-4xl mb-6">
              Built for <span className="text-primary-700 dark:text-primary-400">UK Local Businesses</span>
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              <strong className="text-foreground">500+ UK businesses have quietly made this shift.</strong> From window installers in Middlesbrough to healthcare clinics in London, successful businesses share the same secret.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 lg:grid-cols-4 gap-6 max-w-6xl mx-auto">
            {[
              'Window & Door Installers',
              'Home Improvement',
              'Healthcare Clinics',
              'Beauty & Wellness',
              'Automotive Services',
              'Professional Services',
              'Fitness Studios',
              'Dental Practices',
              'Plumbing & Heating',
              'Electrical Services',
              'Roofing Companies',
              'Landscaping Services'
            ].map((business, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.05 }}
                viewport={{ once: true }}
                className="bg-background border border-primary-200/20 dark:border-primary-800/20 rounded-2xl p-4 text-center"
              >
                <CheckCircle className="w-6 h-6 text-primary-700 dark:text-primary-400 mx-auto mb-2" />
                <p className="text-foreground font-medium text-sm">{business}</p>
              </motion.div>
            ))}
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            viewport={{ once: true }}
            className="text-center mt-12"
          >
            <Link
              href="/demo"
              className="inline-flex items-center justify-center px-8 py-4 bg-primary-700 hover:bg-primary-800 dark:bg-primary-600 dark:hover:bg-primary-700 text-white rounded-2xl transition-all duration-300 font-medium shadow-lg hover:shadow-xl text-lg"
            >
              Discover What Thriving Owners Know
              <ArrowRight className="w-5 h-5 ml-2" />
            </Link>
          </motion.div>
        </div>
      </section>
    </div>
  )
}
