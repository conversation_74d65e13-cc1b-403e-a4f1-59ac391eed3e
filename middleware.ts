import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { logRedirectInVercel } from './lib/vercel-redirect-monitor'

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Early return for paths that should never be redirected
  if (
    pathname.startsWith('/api/') ||
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/favicon') ||
    pathname.includes('.') || // Files with extensions
    pathname === '/robots.txt' ||
    pathname === '/sitemap.xml'
  ) {
    return NextResponse.next()
  }

  // Redirect old/missing service pages to existing ones
  const serviceRedirects: { [key: string]: string } = {
    '/services/lead-conversion': '/services/business-automation',
    '/services/revenue-growth': '/services/business-automation',
    '/services/sms-automation': '/services/business-automation',
    '/services/email-automation': '/services/business-automation',
    '/services/booking-system': '/services/business-automation',
    '/services/customer-conversion': '/services/business-automation',
    '/services/sales-optimization': '/services/business-automation',
  }

  // Check if the current path needs to be redirected
  if (serviceRedirects[pathname]) {
    const toPath = serviceRedirects[pathname]
    logRedirectInVercel(pathname, toPath, 'service', request)
    return NextResponse.redirect(new URL(toPath, request.url), 301)
  }

  // Handle trailing slash redirects (ensure consistency)
  // Only for paths that don't end with known file extensions
  if (pathname !== '/' && pathname.endsWith('/') && !pathname.includes('.')) {
    const newPath = pathname.slice(0, -1)
    logRedirectInVercel(pathname, newPath, 'trailing_slash', request)
    return NextResponse.redirect(new URL(newPath, request.url), 301)
  }

  // Handle common old URLs that might be causing issues
  const commonRedirects: { [key: string]: string } = {
    '/automation': '/services/business-automation',
    '/booking': '/services/business-automation',
    '/sms': '/services/business-automation',
    '/email': '/services/business-automation',
    '/leads': '/services/business-automation',
    '/conversion': '/services/business-automation',
    '/business-solutions': '/solutions',
    '/local-business': '/solutions',
    '/uk-business': '/solutions',
    '/sales': '/contact',
    '/consultation': '/contact',
    '/quote': '/contact',
    '/get-started': '/demo',
    '/start': '/demo',
    '/trial': '/demo',
  }

  if (commonRedirects[pathname]) {
    const toPath = commonRedirects[pathname]
    logRedirectInVercel(pathname, toPath, 'common', request)
    return NextResponse.redirect(new URL(toPath, request.url), 301)
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * More specific matcher to avoid interference with Next.js internals
     * Only match paths that could potentially need redirects
     * Exclude all static files, API routes, and Next.js internals
     */
    '/((?!api|_next|favicon|robots|sitemap|.*\\.[a-zA-Z0-9]+$).*)',
  ],
}
