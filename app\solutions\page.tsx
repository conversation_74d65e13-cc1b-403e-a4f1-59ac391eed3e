import { Metadata } from 'next'
import { SchemaMarkup, BreadcrumbSchema } from '@/components/seo/SchemaMarkup'
import SolutionsClient from './SolutionsClient'

export const metadata: Metadata = {
  title: 'Business Solutions for UK Local Businesses | GenLogic',
  description: 'Discover the solutions that successful UK businesses use to convert 89% of prospects into customers. Join the quiet revolution transforming local business operations.',
  keywords: 'business solutions UK, local business automation, customer conversion solutions, business transformation UK, automation solutions',
  openGraph: {
    title: 'Business Solutions for UK Local Businesses',
    description: 'Discover the solutions that successful UK businesses use to convert 89% of prospects into customers. Join the quiet revolution transforming local business operations.',
    type: 'website',
    url: 'https://genlogic.io/solutions',
    images: [
      {
        url: 'https://genlogic.io/og-solutions.webp',
        width: 1200,
        height: 630,
        alt: 'GenLogic Business Solutions for UK Local Businesses',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Business Solutions for UK Local Businesses',
    description: 'Discover the solutions that successful UK businesses use to convert 89% of prospects into customers. Join the quiet revolution transforming local business operations.',
  },
}

export default function SolutionsPage() {
  const breadcrumbItems = [
    { name: 'Home', url: 'https://genlogic.io' },
    { name: 'Solutions', url: 'https://genlogic.io/solutions' }
  ]

  return (
    <>
      <SchemaMarkup
        type="service"
        data={{
          name: 'Business Solutions for UK Local Businesses',
          description: 'Complete business solutions helping UK local businesses convert 89% of prospects into customers through automation and optimization.'
        }}
      />
      <SchemaMarkup type="organization" />
      <BreadcrumbSchema items={breadcrumbItems} />

      <SolutionsClient />
    </>
  )
}
