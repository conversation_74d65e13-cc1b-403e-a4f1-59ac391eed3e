'use client'

import { motion } from 'framer-motion'
import { CheckCircle, Clock, Star, ArrowRight, Zap, Users, TrendingUp } from 'lucide-react'
import { SchemaMarkup, BreadcrumbSchema } from '@/components/seo/SchemaMarkup'
import { ServicePageSEO } from '@/components/seo/SEOHead'
import { seoConfig, ukBusinessMetrics } from '@/lib/seo-config'
import Link from 'next/link'

// Metadata will be handled by Next.js App Router metadata API

export const metadata = {
  title: 'Business Automation Software for UK Local Businesses | GenLogic',
  description: 'Complete business automation software for UK local businesses. Automate bookings, reduce no-shows by 85%, and save 20+ hours weekly. Trusted by 500+ UK businesses.',
  keywords: 'business automation UK, booking automation, SMS reminders, customer management, reduce no shows, UK business software',
  openGraph: {
    title: 'Business Automation Software for UK Local Businesses',
    description: 'Complete business automation software for UK local businesses. Automate bookings, reduce no-shows by 85%, and save 20+ hours weekly.',
    type: 'website',
    url: 'https://genlogic.io/services/business-automation',
  },
}

export default function BusinessAutomationPage() {
  const breadcrumbItems = [
    { name: 'Home', url: 'https://genlogic.io' },
    { name: 'Services', url: 'https://genlogic.io/services' },
    { name: 'Business Automation', url: 'https://genlogic.io/services/business-automation' }
  ]

  const automationFeatures = [
    {
      icon: <Zap className="w-8 h-8" />,
      title: 'Never Miss Another Lead',
      description: 'Instant auto-responses capture every inquiry, even at 2am. 24/7 booking system ensures no prospect slips away',
      benefit: 'Convert 30% more prospects who would otherwise go to competitors'
    },
    {
      icon: <Clock className="w-8 h-8" />,
      title: 'Smart Follow-Up Sequences',
      description: 'Automated nurturing keeps you top-of-mind until prospects are ready to buy',
      benefit: 'Turn cold leads into hot customers automatically'
    },
    {
      icon: <Users className="w-8 h-8" />,
      title: 'Turn One-Time Buyers Into Repeat Customers',
      description: 'Automated thank you messages, review requests, and re-booking invitations build loyalty',
      benefit: 'Increase repeat business by 40% and customer lifetime value'
    },
    {
      icon: <TrendingUp className="w-8 h-8" />,
      title: 'Track Your Conversion Success',
      description: 'See exactly how many prospects convert into customers and identify revenue opportunities',
      benefit: 'Optimize your sales process for maximum revenue'
    }
  ]

  const businessTypes = [
    'Hair Salons & Beauty',
    'Fitness Studios & Gyms',
    'Healthcare Clinics',
    'Automotive Services',
    'Home Improvement',
    'Professional Services'
  ]

  return (
    <>
      <SchemaMarkup 
        type="service" 
        data={{
          name: 'Business Automation Software',
          description: 'Complete business automation software for UK local businesses. Automate bookings, reduce no-shows, and save time.'
        }}
      />
      <SchemaMarkup type="softwareApplication" />
      <SchemaMarkup type="faq" data={{
        faqs: [
          {
            question: "How quickly can business automation be set up?",
            answer: "Most UK businesses are fully automated within 2-3 days. We handle the complete setup including GoHighLevel configuration, SMS/email templates, and staff training."
          },
          {
            question: "Will automation work with my existing booking system?",
            answer: "Yes, our automation integrates with most existing systems or we can provide a complete replacement. We ensure a smooth transition with zero downtime."
          },
          {
            question: "How much does business automation cost?",
            answer: "Our automation plans start from £120/month for the Silver Plan. This typically saves businesses £2,000+ monthly through reduced no-shows and increased efficiency."
          },
          {
            question: "Can customers still speak to a human when needed?",
            answer: "Absolutely. Our automation handles routine tasks while ensuring customers can always reach you for complex inquiries. It enhances rather than replaces human interaction."
          },
          {
            question: "What types of UK businesses benefit most from automation?",
            answer: "Local service businesses like window installers, home improvement companies, healthcare clinics, salons, and professional services see the biggest benefits from our automation."
          }
        ]
      }} />
      <BreadcrumbSchema items={breadcrumbItems} />

      <div className="relative w-full overflow-hidden">
        {/* Hero Section */}
        <section className="header-spacing py-20 relative">
          <div className="container relative z-10 mx-auto px-4 md:px-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, ease: 'easeOut' }}
              className="mx-auto max-w-4xl text-center"
            >
              <div className="bg-background border border-primary-200/20 dark:border-primary-800/20 rounded-3xl p-12 shadow-lg backdrop-blur-sm">
                <h1 className="text-4xl font-bold text-foreground sm:text-5xl md:text-6xl mb-6">
                  Turn More Prospects Into <span className="text-green-700 dark:text-green-400">Paying Customers</span>
                </h1>

                <p className="text-xl text-muted-foreground leading-relaxed mb-6">
                  <strong className="text-foreground">Most UK businesses convert only 15-20% of prospects.</strong> GenLogic's customer conversion system helps you convert 89% through smart follow-up automation.
                </p>
                
                <div className="grid md:grid-cols-3 gap-6 mb-8">
                  <div className="bg-green-50 dark:bg-green-950/30 border border-green-200/30 dark:border-green-800/30 rounded-2xl p-4">
                    <div className="text-2xl font-bold text-green-700 dark:text-green-400 mb-1">
                      89%
                    </div>
                    <p className="text-sm text-muted-foreground">Conversion rate achieved</p>
                  </div>

                  <div className="bg-green-50 dark:bg-green-950/30 border border-green-200/30 dark:border-green-800/30 rounded-2xl p-4">
                    <div className="text-2xl font-bold text-green-700 dark:text-green-400 mb-1">
                      {ukBusinessMetrics.revenueIncrease}
                    </div>
                    <p className="text-sm text-muted-foreground">Additional revenue yearly</p>
                  </div>

                  <div className="bg-green-50 dark:bg-green-950/30 border border-green-200/30 dark:border-green-800/30 rounded-2xl p-4">
                    <div className="text-2xl font-bold text-green-700 dark:text-green-400 mb-1">
                      500+
                    </div>
                    <p className="text-sm text-muted-foreground">UK businesses growing</p>
                  </div>
                </div>
                
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Link 
                    href="/demo" 
                    className="inline-flex items-center justify-center px-8 py-4 bg-primary-700 hover:bg-primary-800 dark:bg-primary-600 dark:hover:bg-primary-700 text-white rounded-2xl transition-all duration-300 font-medium shadow-lg hover:shadow-xl text-lg"
                  >
                    See Business Automation Demo
                    <ArrowRight className="w-5 h-5 ml-2" />
                  </Link>
                  
                  <Link 
                    href="/pricing" 
                    className="inline-flex items-center justify-center px-8 py-4 border-2 border-primary-200 dark:border-primary-800 text-primary-700 dark:text-primary-400 rounded-2xl hover:border-primary-300 dark:hover:border-primary-700 hover:bg-primary-50 dark:hover:bg-primary-900/20 transition-all duration-300 font-medium text-lg"
                  >
                    View Pricing
                  </Link>
                </div>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Automation Features */}
        <section className="py-20">
          <div className="container mx-auto px-4 md:px-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, ease: 'easeOut' }}
              viewport={{ once: true }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl font-bold text-foreground sm:text-4xl mb-6">
                Powerful <span className="text-primary-700 dark:text-primary-400">Automation Features</span>
              </h2>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                <strong className="text-foreground">Every feature designed to save you time and grow your business.</strong> No complex setup, no technical knowledge required.
              </p>
            </motion.div>

            <div className="grid md:grid-cols-2 gap-8 max-w-6xl mx-auto">
              {automationFeatures.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-background border border-primary-200/20 dark:border-primary-800/20 rounded-3xl p-8 shadow-lg"
                >
                  <div className="flex items-start gap-4">
                    <div className="flex-shrink-0 w-12 h-12 bg-primary-100 dark:bg-primary-900/30 rounded-2xl flex items-center justify-center text-primary-700 dark:text-primary-400">
                      {feature.icon}
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-semibold text-foreground mb-3">
                        {feature.title}
                      </h3>
                      <p className="text-muted-foreground mb-4 leading-relaxed">
                        {feature.description}
                      </p>
                      <div className="bg-primary-50 dark:bg-primary-950/30 border border-primary-200/30 dark:border-primary-800/30 rounded-xl p-3">
                        <p className="text-sm text-foreground font-medium">
                          <CheckCircle className="w-4 h-4 text-primary-700 dark:text-primary-400 inline mr-2" />
                          {feature.benefit}
                        </p>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Business Types */}
        <section className="py-20">
          <div className="container mx-auto px-4 md:px-6">
            <div className="bg-primary-50 dark:bg-primary-950/30 border border-primary-200/30 dark:border-primary-800/30 rounded-3xl p-12 shadow-lg max-w-4xl mx-auto text-center">
              <h2 className="text-3xl font-bold text-foreground mb-6">
                Perfect for UK <span className="text-primary-700 dark:text-primary-400">Local Businesses</span>
              </h2>
              <p className="text-xl text-muted-foreground mb-8">
                <strong className="text-foreground">Trusted by 500+ UK businesses</strong> across multiple industries to automate their operations and grow revenue.
              </p>
              
              <div className="grid md:grid-cols-3 gap-4 mb-8">
                {businessTypes.map((type, index) => (
                  <div key={index} className="bg-background border border-primary-200/20 dark:border-primary-800/20 rounded-2xl p-4">
                    <CheckCircle className="w-5 h-5 text-primary-700 dark:text-primary-400 mx-auto mb-2" />
                    <p className="text-foreground font-medium">{type}</p>
                  </div>
                ))}
              </div>
              
              <Link 
                href="/demo" 
                className="inline-flex items-center justify-center px-8 py-4 bg-primary-700 hover:bg-primary-800 dark:bg-primary-600 dark:hover:bg-primary-700 text-white rounded-2xl transition-all duration-300 font-medium shadow-lg hover:shadow-xl text-lg"
              >
                Start Your Free Trial
                <ArrowRight className="w-5 h-5 ml-2" />
              </Link>
            </div>
          </div>
        </section>
      </div>
    </>
  )
}
